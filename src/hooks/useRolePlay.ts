import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {useSharedValue, withSpring, withTiming} from 'react-native-reanimated';
import {goBack} from '../navigation/NavigationServices';
import {
  fetchChatbot,
  fetchRolePlayCard,
  fetchChatbotPhonemes,
} from '../redux/reducer/fetchData';
import {useReduxDispatch} from '../redux/store';
import useAudioRecorder from './useAudioRecorder';
import useSound from './useSound';
import {isAndroid} from '../utils/Scale';

export const useRolePlay = (scrollCurtainRef?: React.RefObject<any>) => {
  const {recording, audioPath, startRecording, stopRecording} =
    useAudioRecorder();
  const {changeSource, stop} = useSound();
  const dispatch = useReduxDispatch();

  const [rolePlayData, setRolePlayData] = useState<RolePlayCard[]>([]);
  const [modalState, setModalState] = useState({
    visible: false,
    content: null as RolePlayCard | null,
  });

  const [uiState, setUIState] = useState({
    showMission: false,
    quitModal: false,
    ccActive: false,
    ideaActive: false,
  });

  const [apiLoading, setApiLoading] = useState<boolean>(false);
  const [sessionId, setSessionId] = useState<string>('');
  const [rolePlayResponse, setRolePlayResponse] = useState<any>();

  const trasnY = useSharedValue(-300);
  const opacity = useSharedValue(0);
  const speechBubbleRef = useRef<any>(null);
  const idleTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    fetchRolePlayData();
  }, []);

  useEffect(() => {
    setTimeout(() => {
      trasnY.value = withSpring(0);
      opacity.value = withTiming(1, {duration: 300});
    }, 200);
  }, []);

  useEffect(() => {
    opacity.value = withTiming(0, {duration: 150});
    opacity.value = withTiming(1, {duration: 200});
  }, [modalState.content?.rlFigureCode]);

  const cancelIdleTimeout = () => {
    if (idleTimeoutRef.current) {
      clearTimeout(idleTimeoutRef.current);
      idleTimeoutRef.current = null;
    }
  };

  const startIdleTimeout = () => {
    cancelIdleTimeout();
    idleTimeoutRef.current = setTimeout(() => {
      speechBubbleRef?.current?.show?.(
        'Open a card and shine on stage?',
        false,
      );
    }, 3000);
  };

  const fetchRolePlayData = async () => {
    setApiLoading(true);
    try {
      const result = await dispatch(fetchRolePlayCard());
      if (result?.payload.data) {
        setRolePlayData(result?.payload.data);
      }
    } catch (error) {
      setRolePlayData([]);
    }
    setApiLoading(false);
  };
  const handleChooseCard = (item: RolePlayCard) => {
    setModalState(prev => ({
      ...prev,
      content: item,
    }));
    setTimeout(() => {
      speechBubbleRef?.current?.hide();
      setModalState(prev => ({
        ...prev,
        visible: true,
      }));
    }, 200);
    cancelIdleTimeout();
  };

  const handleCloseModal = useCallback(() => {
    setModalState(prev => ({
      ...prev,
      visible: false,
    }));
  }, []);

  const closeQuitModal = useCallback(() => {
    setUIState(prev => ({
      ...prev,
      quitModal: false,
    }));
  }, []);

  const handleQuit = useCallback(() => {
    setUIState(prev => ({
      ...prev,
      quitModal: false,
    }));
    setTimeout(() => {
      goBack();
    }, 300);
  }, []);

  const onBackPress = useCallback(() => {
    if (uiState.showMission) {
      setUIState(prev => ({
        ...prev,
        quitModal: true,
      }));
    } else {
      goBack();
    }
  }, [uiState.showMission]);

  const callShowMissionAPI = async () => {
    setApiLoading(true);
    const result = await dispatch(
      fetchChatbot({
        rlId: modalState?.content?.id,
      }),
    );
    const {data} = result?.payload;
    changeSource(data?.questionPath);
    setSessionId(data?.sessionId);
    setRolePlayResponse(data);
    setApiLoading(false);
  };

  const handleStartNow = useCallback(async () => {
    handleCloseModal();
    setUIState(prev => ({
      ...prev,
      showMission: true,
    }));
    trasnY.value = withTiming(-300, {duration: 300});
    speechBubbleRef?.current?.hide();
    setTimeout(() => {
      scrollCurtainRef?.current?.show();
    }, 1000);

    callShowMissionAPI();
  }, [handleCloseModal, callShowMissionAPI, trasnY, scrollCurtainRef]);

  const handleCCPress = () => {
    stop();
    setUIState(prev => {
      const nextState = !prev.ccActive;

      if (nextState) {
        speechBubbleRef?.current?.show?.(
          rolePlayResponse?.question || rolePlayResponse?.questionText,
          false,
          rolePlayResponse?.questionPath,
        );
      } else {
        speechBubbleRef?.current?.hide?.();
      }

      return {
        ...prev,
        ccActive: nextState,
        ideaActive: nextState ? false : prev.ideaActive,
      };
    });
  };

  const handleIdeaPress = () => {
    stop();
    setUIState(prev => {
      const nextState = !prev.ideaActive;

      if (nextState) {
        speechBubbleRef?.current?.show?.(rolePlayResponse?.idea || '', true);
      } else {
        speechBubbleRef?.current?.hide?.();
      }

      return {
        ...prev,
        ideaActive: nextState,
        ccActive: nextState ? false : prev.ccActive,
      };
    });
  };

  const handleRecordingCallback = async () => {
    stop();
    if (!recording) {
      setUIState(prev => ({
        ...prev,
        ccActive: false,
        ideaActive: false,
      }));
      speechBubbleRef?.current?.hide?.();

      setTimeout(async () => {
        await startRecording();
      }, 0);
      return;
    }

    setTimeout(async () => {
      try {
        await stopRecording();

        if (!audioPath) {
          console.warn('Không có file để upload!');
          return;
        }

        const formData = new FormData();
        const fileName = `audio_${Date.now()}.${isAndroid ? 'mp4' : 'm4a'}`;

        formData.append('file', {
          uri: audioPath,
          name: fileName,
          type: isAndroid ? 'audio/mp4' : 'audio/m4a',
        });
        formData.append(
          'rlRequestDto',
          JSON.stringify({
            rlId: modalState?.content?.id || '',
            sessionId: sessionId || '',
          }),
        );

        const result = await dispatch(fetchChatbotPhonemes(formData));

        if (result?.payload) {
          const {data} = result?.payload;
          setRolePlayResponse(data);
          changeSource(data?.questionPath);

          if (data?.score && Array.isArray(data.score)) {
            setModalState(prev => {
              if (!prev.content) return prev;

              const updatedMissions = prev.content.mission.map(mission => {
                const scoreItem = data.score.find(
                  (score: any) => score.key === mission.key,
                );
                return {
                  ...mission,
                  status: scoreItem?.value === 'done',
                };
              });

              const hasNewCompletion = updatedMissions.some(
                (mission, index) =>
                  mission.status && !prev.content?.mission[index]?.status,
              );

              if (hasNewCompletion) {
                setTimeout(() => {
                  scrollCurtainRef?.current?.animateTaskCompleted();
                }, 500);
              }

              return {
                ...prev,
                content: {
                  ...prev.content,
                  mission: updatedMissions,
                },
              };
            });
          }

          // speakWord(message);
          // setSessionId(newSessionId);
        }

        // scrollCurtainRef?.current?.animateTaskCompleted();
        // const phonemeMessage = phonemeRes?.payload?.data?.message;
        // const nextSessionId = phonemeRes?.payload?.data?.sessionId || sessionId;

        // if (phonemeMessage) {
        //   speakWord(phonemeMessage);
        //   setSessionId(nextSessionId);
        //   return;
        // }

        // const chatbotRes = await dispatch(
        //   fetchChatbot({
        //     sessionId: nextSessionId,
        //     message: phonemeMessage,
        //   }),
        // );

        // if (chatbotRes?.payload) {
        //   const {message, sessionId: newSessionId} = chatbotRes?.payload?.data;
        //   speakWord(message);
        //   setSessionId(newSessionId);
        // }
      } catch (error) {
        console.error('Recording callback error:', error);
      }
    }, 0);
  };

  const handleRandomCard = () => {
    fetchRolePlayData();
  };

  return {
    modalVisible: modalState.visible,
    modalContent: modalState.content,
    showMission: uiState.showMission,
    quitModal: uiState.quitModal,
    ccActive: uiState.ccActive,
    ideaActive: uiState.ideaActive,
    speechBubbleRef,
    rolePlayResponse,
    trasnY,
    opacity,
    rolePlayData,
    apiLoading,
    handleChooseCard,
    handleCloseModal,
    closeQuitModal,
    handleQuit,
    onBackPress,
    handleStartNow,
    handleCCPress,
    handleIdeaPress,
    handleRecordingCallback,
    handleRandomCard,
    startIdleTimeout,
    cancelIdleTimeout,
  };
};
