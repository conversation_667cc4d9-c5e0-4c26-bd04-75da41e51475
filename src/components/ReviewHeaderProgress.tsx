import React, {useEffect, useMemo, useState} from 'react';
import {StyleSheet, TouchableOpacity, View} from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {goBack} from '../navigation/NavigationServices';
import {HIT_SLOP, initTop, widthScreen} from '../utils/Scale';
import TextApp from './TextApp';
import IconClosed from '../../assets/svgIcons/IconClosed.tsx';
import {useTheme} from '../hooks/useTheme.ts';
import TimerDisplay from './TimerDisplay';
import Spacer from './Spacer.tsx';
import IconHeart from '../../assets/svgIcons/HeartIcon.tsx';
import {Theme} from '../themes';
import {moderateScale, moderateVerticalScale} from 'react-native-size-matters';
import {useTimer} from '../hooks/useTimer';

type ReviewHeaderProgressType = {
  step: number;
  steps: number;
  height: number;
  total: number;
  isReview?: boolean;
  initialSeconds?: number;
  isTimerActive?: boolean;
  completionTime?: number;
  onPressRight: () => void;
  heart: string | number;
  disableSkip?: boolean;
  onTimerUpdate?: (secondsLeft: number) => void;
};

export const ReviewHeaderProgress = React.memo(
  ({
    step = 0,
    steps,
    height,
    total,
    isReview,
    initialSeconds = 0,
    isTimerActive = false,
    completionTime = 0,
    onPressRight,
    heart,
    disableSkip,
    onTimerUpdate,
  }: ReviewHeaderProgressType) => {
    const theme = useTheme();
    const {formattedTime, secondsLeft} = useTimer({
      initialSeconds,
      isActive: isTimerActive,
      onTimeUp: () => {},
    });

    React.useEffect(() => {
      if (onTimerUpdate) {
        onTimerUpdate(secondsLeft);
      }
    }, [secondsLeft, onTimerUpdate]);

    const styles = useMemo(() => createStyles(theme), [theme]);
    const [width, setWidth] = useState(0);
    const animatedValue = useSharedValue(0);
    const animatedStyle = useAnimatedStyle(() => {
      return {
        transform: [{translateX: animatedValue.value}],
      };
    });

    useEffect(() => {
      if (width > 0) {
        animatedValue.value = withTiming((width * step) / steps - width, {
          duration: 300,
        });
      }
    }, [step, width]);

    if (isReview) {
      return (
        <View style={styles.container}>
          <View style={[styles.contentBar, {justifyContent: 'center'}]}>
            <TouchableOpacity
              onPress={() => goBack()}
              style={{
                padding: Theme.spacing.spacing_md,
                position: 'absolute',
                left: 12,
              }}>
              <IconClosed />
            </TouchableOpacity>

            <TextApp
              text={`Review your mistakes`}
              preset="text_md_semibold"
              textColor={theme.text_secondary}
              style={{
                paddingVertical: 10,
              }}
            />
          </View>
        </View>
      );
    }

    return (
      <View style={styles.container}>
        <View style={[styles.contentBar, {paddingHorizontal: 60}]}>
          <TextApp
            text={`Question ${step}/${total}`}
            preset="text_md_semibold"
            textColor={theme.text_secondary}
          />

          <TimerDisplay formattedTime={formattedTime} />
        </View>
        <Spacer size={8} direction={'vertical'} />
        <View style={styles.contentBar}>
          <TouchableOpacity onPress={goBack} hitSlop={HIT_SLOP}>
            <IconClosed />
          </TouchableOpacity>
          <View style={{flex: 1, marginHorizontal: moderateScale(12)}}>
            <View
              onLayout={e => setWidth(e.nativeEvent.layout.width)}
              style={[
                styles.progressContainer,
                {height, borderRadius: height},
              ]}>
              <Animated.View
                style={[
                  styles.progressBar,
                  {
                    width,
                    height,
                    borderRadius: height,
                    backgroundColor: theme.fg_brand_secondary,
                  },
                  animatedStyle,
                ]}
              />
            </View>
          </View>
          <IconHeart />
          <Spacer size={4} direction={'horizontal'} />
          <TextApp
            text={heart}
            preset={'text_xs_medium'}
            textColor={theme.text_quaternary}
          />
        </View>
      </View>
    );
  },
);

const createStyles = (theme: any) =>
  StyleSheet.create({
    container: {
      position: 'absolute',
      paddingTop: initTop,
      zIndex: 100,
      backgroundColor: theme.bg_primary,
      paddingBottom: moderateVerticalScale(8),
    },
    progressContainer: {
      backgroundColor: '#D9D9D9',
      overflow: 'hidden',
    },

    progressBar: {
      backgroundColor: '#F99A3D',
      left: 0,
      top: 0,
    },
    contentBar: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      width: widthScreen,
      paddingHorizontal: 12,
    },
    btnRight: {
      padding: Theme.spacing.spacing_md,
      borderRadius: Theme.radius.radius_md,
      borderColor: theme.border_primary,
      borderWidth: 1,
    },
  });
