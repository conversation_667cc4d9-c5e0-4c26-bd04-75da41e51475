import React from 'react';
import Animated, {ZoomIn, ZoomOut} from 'react-native-reanimated';
import ReviewSelectTemplate from './templates/ReviewSelectTemplate';
import WellDone from './templates/WellDone';
import {goBack, replace} from '../navigation/NavigationServices';
import {APP_SCREEN} from '../navigation/screenType';

interface ReviewQuestionContentProps {
  currentPage: number;
  done: boolean;
  data: any[];
  passed: number;
  total: number;
  onPreview: () => void;
  styles: any;
  reviewItem?: any; // Thêm prop để truyền dữ liệu review
}

const ReviewQuestionContent: React.FC<ReviewQuestionContentProps> = React.memo(
  ({currentPage, done, data, passed, total, onPreview, styles, reviewItem}) => {
    console.log('ReviewQuestionContent render:', {
      currentPage,
      done,
      dataLength: data?.length,
      data: data,
      passed,
      total
    });

    return (
      <Animated.View
        key={currentPage}
        entering={ZoomIn}
        exiting={ZoomOut}
        style={styles.centerBox}>
        {done ? (
          <WellDone
            onPress={() => goBack()} // Continue - quay về Practice
            answer={`${passed}/${total}`}
            onPreview={() => {
              // Review - replace để khi back sẽ về Practice
              replace(APP_SCREEN.REVIEW_QUESTIONS, {
                item: reviewItem,
                isDone: true // Đánh dấu là review mode
              });
            }}
          />
        ) : (
          <ReviewSelectTemplate data={data[currentPage]} />
        )}
      </Animated.View>
    );
  },
);

ReviewQuestionContent.displayName = 'ReviewQuestionContent';

export default ReviewQuestionContent;
