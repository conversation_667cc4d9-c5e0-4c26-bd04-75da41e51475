import React, {RefObject} from 'react';
import {
  ImageBackground,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import Animated, {ZoomIn, ZoomOut} from 'react-native-reanimated';
import {moderateVerticalScale, scale} from 'react-native-size-matters';
import {SvgIcons} from '../../../assets/svg';
import {RecordButtonWithRipple} from '../../../common/animation/RippleRecordButton';
import {heightScreen} from '../../utils/Scale';
import TextApp from '../TextApp';
import {Theme} from '../../themes';
import {OptimizedScrollCurtainRef} from './OptimizedScrollCurtain';
import TextTranslator from '../sound/TextTranslator';

interface RolePlayMissionViewProps {
  ccActive: boolean;
  ideaActive: boolean;
  apiLoading?: boolean;
  onCCPress: () => void;
  onIdeaPress: () => void;
  onRecordingCallback: () => void;
  scrollCurtainRef?: RefObject<OptimizedScrollCurtainRef>;
  answerText: string;
  scores: any;
  disable: boolean;
  audio: string;
}

export const RolePlayMissionView: React.FC<RolePlayMissionViewProps> =
  React.memo(
    ({
      ccActive,
      ideaActive,
      apiLoading = false,
      onCCPress,
      onIdeaPress,
      onRecordingCallback,
      scrollCurtainRef,
      answerText,
      scores,
      disable,
      audio,
    }) => {
      const handleCCPress = () => {
        scrollCurtainRef?.current?.hide();
        setTimeout(() => {
          onCCPress();
        }, 200);
      };

      const handleIdeaPress = () => {
        scrollCurtainRef?.current?.hide();
        setTimeout(() => {
          onIdeaPress();
        }, 200);
      };

      const handleRecordingCallback = () => {
        scrollCurtainRef?.current?.hide();
        setTimeout(() => {
          onRecordingCallback();
        }, 200);
      };
      return (
        <Animated.View
          entering={ZoomIn.delay(500)}
          exiting={ZoomOut.delay(500)}
          style={styles.container}>
          <ImageBackground
            source={Theme.images.subFrameRolePlay}
            style={styles.background}>
            <View style={styles.recordButtonContainer}>
              <RecordButtonWithRipple
                callBackRecording={handleRecordingCallback}
                disabled={apiLoading || disable}
              />
            </View>
            <TextTranslator
              disable={false}
              text={answerText}
              scores={scores?.word_scores}
              children={!audio}
              audio={audio}
            />
          </ImageBackground>

          <View style={styles.bottomButtonsContainer}>
            <TouchableOpacity
              style={[
                styles.actionButton,
                {backgroundColor: ccActive ? '#FFF3AB' : '#7F5500'},
              ]}
              disabled={disable}
              onPress={handleCCPress}>
              <TextApp
                preset="text_md_bold"
                text={'CC'}
                textColor={ccActive ? '#7F5500' : '#FFF3AB'}
                style={styles.actionButtonText}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.actionButton,
                {backgroundColor: ideaActive ? '#FFF3AB' : '#7F5500'},
              ]}
              disabled={disable}
              onPress={handleIdeaPress}>
              {ideaActive ? <SvgIcons.IdeaActive /> : <SvgIcons.Idea />}
            </TouchableOpacity>
          </View>
        </Animated.View>
      );
    },
  );

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    alignSelf: 'center',
    bottom: (160 / 2436) * heightScreen,
  },
  background: {
    width: 344.22,
    height: 135.86,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordButtonContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: -70,
  },
  headerText: {
    lineHeight: 24,
    textAlign: 'center',
    marginTop: 28,
  },
  missionWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: 10,
    marginTop: 5,
  },
  missionItem: {
    width: '45%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  missionStatusIndicator: {
    width: 20,
    height: 20,
    borderRadius: 20,
    borderWidth: 0.5,
    borderColor: '#D5D7DA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  missionText: {
    lineHeight: 24,
    marginLeft: 5,
  },
  bottomButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: scale(40),
    marginTop: moderateVerticalScale(5),
  },
  actionButton: {
    width: scale(57),
    height: moderateVerticalScale(32),
    paddingVertical: 5,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  actionButtonText: {
    lineHeight: 20,
  },
});
