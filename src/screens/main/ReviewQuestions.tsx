import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import ReviewQuestionContent from '../../components/ReviewQuestionContent.tsx';
import Pagination from '../../components/Pagination.tsx';
import ModalQuestion from '../../components/ModalQuestion.tsx';
import SkipQuestionModal from '../../components/modal/SkipQuestionModal.tsx';

import {Theme} from '../../themes';
import {useReduxDispatch, useTypedSelector} from '../../redux/store.ts';
import {
  hiddenAnswer,
  hideModalQuestion,
  setDoneQuestion,
  setTimeOutQuest,
} from '../../redux/reducer/QuestionSlice.ts';
import {
  fetchLessonAnswer,
} from '../../redux/reducer/fetchData.ts';
import {APP_SCREEN, RootStackParamList} from '../../navigation/screenType.ts';
import {LoadResource} from '../../components/LoadResource.tsx';
import {ReviewHeaderProgress} from '../../components/ReviewHeaderProgress.tsx';
import {QuotesModal} from '../../components/QuotesModal.tsx';
import {controllerSoundBg} from '../../redux/reducer/ProfileSlice.ts';
import useSound from '../../hooks/useSound.ts';
import useQuestion from '../../hooks/auth/useQuestion.ts';

type ReviewQuestionProps = NativeStackScreenProps<
  RootStackParamList,
  APP_SCREEN.REVIEW_QUESTIONS
>;

const getModalContent = () => ({
  title: 'Time\'s over!',
  content: 'Don\'t worry. Try again!',
  btnText: 'Continue',
});

const ReviewQuestions: React.FC<ReviewQuestionProps> = ({route}) => {
  const {item, isDone} = route.params;
  const dispatch = useReduxDispatch();
  const {loading, dataAnswer} = useTypedSelector(state => state.lesson);
  const {isShowQuotes, isShowAnswer, dataFinish, answer, isTimeOut} =
    useTypedSelector(state => state.question);

  const [data, setData] = useState<any>(item?.data);
  const [currentPage, setCurrentPage] = useState(0);
  const [done, setDone] = useState(false);
  const [preview, setPreview] = useState(isDone);
  const [visibleSkip, setVisibleSkip] = useState(false);
  const [dontShowAgain, setDontShowAgain] = useState(false);
  const {stop} = useSound();
  useQuestion();

  const initialTimerSeconds = useMemo(() => {
    const duration = item?.duration || 720;
    return typeof duration === 'number' && duration > 60 ? duration : duration * 60;
  }, [item?.duration]);

  const currentSecondsLeftRef = useRef(initialTimerSeconds);
  const uniqueData = Array.from(
    new Map(dataFinish.map(item => [item.id, item])).values(),
  );
  const passed = uniqueData.filter(i => i.isPassExcercise)?.length || 0;
  const isTimerActive = !done && !preview;

  const handleTimerUpdate = useCallback((sec: number) => {
    currentSecondsLeftRef.current = sec;
    if (sec === 1) {
      setVisibleSkip(true);
    }
  }, []);

  const handleEnd = useCallback(() => {
    setDone(true);
    handleDone();
    setVisibleSkip(false);
    dispatch(setTimeOutQuest(false));
  }, []);

  const handleDone = useCallback(() => {
    console.log('Review lesson completed');
  }, [dataFinish, data?.length, passed, uniqueData]);

  const handleNextPage = useCallback(() => {
    dispatch(hideModalQuestion());
    if (currentPage < data?.length - 1) {
      setCurrentPage(prev => prev + 1);
    } else {
      setDone(true);
      setCurrentPage(0);
    }
  }, [currentPage, data?.length]);

  const handleNextPagination = useCallback((index: number) => {
    setCurrentPage(index);
    dispatch(hiddenAnswer());
  }, []);

  const fetchDataLessonAnswer = useCallback(async () => {
    const res = await dispatch(
      fetchLessonAnswer({
        assignId: item.exAssignId,
        lessonId: item.id,
        isHighest: isDone,
      }),
    );
    setData(res.payload.data.data);
  }, [item]);

  useEffect(() => {
    stop();
    dispatch(controllerSoundBg());
    dispatch(setDoneQuestion(preview));

    if (preview) {
      fetchDataLessonAnswer();
    }
  }, [preview]);

  useEffect(() => {
    if (done) {
      handleDone();
    }
  }, [done, dataFinish?.length, data?.length]);

  if (loading) {
    return (
      <View style={styles.container}>
        <LoadResource />
      </View>
    );
  }

  const modalContent = getModalContent();

  return (
    <View style={styles.container}>
      {!done && (
        <ReviewHeaderProgress
          step={currentPage + 1}
          total={data.length}
          steps={data.length}
          height={18}
          isReview={preview}
          initialSeconds={initialTimerSeconds}
          completionTime={dataAnswer?.completionTime}
          isTimerActive={isTimerActive}
          onPressRight={() => {}}
          onTimerUpdate={handleTimerUpdate}
        />
      )}

      <ReviewQuestionContent
        currentPage={currentPage}
        done={done}
        data={data}
        passed={passed}
        total={item?.data?.length}
        onPreview={() => {
          setPreview(true);
          setDone(false);
          setCurrentPage(0);
        }}
        styles={styles}
        reviewItem={item}
      />

      {preview && <Pagination data={data} onPress={handleNextPagination} />}
      {!done && !preview && answer && (
        <QuotesModal
          isVisible={isShowQuotes}
          onContinue={handleNextPage}
          answer={answer}
        />
      )}
      {answer && <ModalQuestion isVisible={isShowAnswer} answer={answer} />}

      <SkipQuestionModal
        {...modalContent}
        isVisible={visibleSkip || (isTimeOut && !preview)}
        onClose={() => setVisibleSkip(false)}
        onConfirm={handleEnd}
        doNotShowAgain={dontShowAgain}
        onToggleDoNotShowAgain={setDontShowAgain}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Theme.colors.white,
  },
});

export default ReviewQuestions;